# 活动组件AI生成规范

### 组件实现规范
1. **目录结构**：
  - 存放项目：hdpt_activity_xlogic_base
  - 存放路径：\hdpt_activity_xlogic_base\src\main\java\com\yy\gameecology\hdzj\element\component
  - 包名：package com.yy.gameecology.hdzj.element.component

2. **常用api使用文档**：
  - 详情见 /docs/api 目录

3. **代码规范**：
   - 以PuzzleComponent组件为例，PuzzleComponent是组件的主逻辑，里面可以有异步事件监听、http接口、定时任务。
   - PuzzleComponentAttr是组件可配置属性，在package com.yy.gameecology.hdzj.element.component.attr包下面。
   - ComponentId定义组件的id，id不可重复
   - 组件属性编码规范，请遵循 组件属性编码规范文档.md 中的规范，特别注意Constant类正确的包名应该是com.yy.gameecology.hdzj.element.attrconfig.Constant，别引用错误

4. **业务数据规范**：
   - cp成员的分隔符是|,前面是用户uid,后面是主播uid,可以用com.yy.gameecology.common.consts.Const.splitCpMember解析
   - 为保证事件处理方法接口幂等性，通常需要获取事件的seq,可以通过com.yy.gameecology.hdzj.BaseActComponent.getEventSeq方法获取

5. **数据类型定义原则**：
   - 涉及小数的字段，不要使用double数据类型，应该使用long 避免浮点数精度问题

## 组件设计共同点抽象总结

### 核心架构模式
1. **事件驱动设计**：
   - 事件处理需实现幂等性
   - 支持事件去重和顺序保证

   
2. **代码数据操作要求**：
   -数据库操作，如果不用写语句的可使用GameecologyDao，但要写语句要另外封装多1个mybatis mapper方法（可参考PepcGameMapper）不要出现拼接字符串情况，避免sql注入

### 编码注意事项
在开始前，请先执行以下信息收集步骤：
1. 查询现有组件属性类的包结构和导入语句
2. 查找ComponentAttr基类的字段定义
3. 确认ComponentAttrField、BizSource、SubField、AwardAttrConfig等类的正确包名
4. 了解现有组件的代码结构和命名规范


   